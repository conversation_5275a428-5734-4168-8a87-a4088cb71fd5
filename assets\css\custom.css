.border {
    border: 1px solid;
}

.hr-color{
    margin-top: 10px;
    border-bottom: 0.5px solid;
    color: #d8341b;
}

.h4-color {
    color: #d8341b;
}

.kan-ban-tab{
    overflow:auto;
}

.font-size-10{
    font-size: 10px;
}

.font-size-14{
    font-size: 14px;
}

.vertical-align-middle{
    vertical-align: middle;
}

.width-300{
    width: 300px;
}

.border-top-2{
    border-top: 2px !important;
}

.border-top-solid{
    border-top-style: solid !important;
}

.color-white{
    color: #fff !important;
}

.container-fluid {
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

.col-md-offset-3 {
  margin-left: 25%;
}

.col-md-6 {
  width: 50%;
}

.padding-50{
    padding: 50px;
}

#unsubscribe .well{
    background-image: linear-gradient(to bottom,#e8e8e8 0,#f5f5f5 100%);
    padding: 1rem !important;
}

#unsubscribe #container {
  border-radius: 10px;
  padding: 20px;
}
#unsubscribe .bg-light {
  background-color: #f8f9fa !important;
}

body{
  background-color: #1e293b !important;
}

