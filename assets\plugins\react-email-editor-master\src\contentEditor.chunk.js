(self.webpackChunkunlayer=self.webpackChunkunlayer||[]).push([[688],{88343:function(e,r,n){"use strict";n.r(r);var t,o=n(27378),a=n(24617),c=n(83573),u=n(73355),l=n(8441),i=n(29902),s=n(97939),f=n(38549),g=o.memo((function(e){var r=e.children,n=e.type,t=e.contentVersion,l=e.mergeTagGroup,g=e.mergeTagRule,v=e.fullScreen,d=void 0!==v&&v,m=e.displayMode,C=e.inline,w=e.fixedToolbarContainer,y=e.autoFocus,b=e.trackKeystrokes,T=e.onSettings,h=e.onInit,k=e.onFormatContent,S=e.onChange,L=(0,f.Z)(),E=(0,a.I0)(),M=(0,a.v9)(i.wl.allowCustomFonts),R=(0,a.v9)(i.wl.getAppearance),j=(0,a.v9)(i.wl.getLocale),Z=(0,a.v9)(i.wl.getTextDirection),_=(0,a.v9)(i.wl.getMergeTags),x=(0,a.v9)(i.wl.getMergeTagsConfig),F=(0,a.v9)(i.wl.getColors),D=(0,a.v9)(i.wl.getSyncVersion),I=(0,c.Z)(),N=(0,o.useRef)(null),O=(0,o.useRef)(void 0),z=(0,o.useRef)(void 0),G=(0,o.useRef)(!1),K=L.hasCallback("mergeTag");(0,o.useEffect)((function(){var e={};G.current=!1,(0,s.j_)(e,{type:n,fullScreen:d}),(0,s.Kj)(e,{recentColors:F}),(0,s.b4)(e,{type:n,fullScreen:d,displayMode:m}),(0,s.bl)(e,{fullScreen:d,displayMode:m}),(0,s.Ze)(e),(0,s.ZX)(e,{type:n,mergeTags:_,mergeTagGroup:l,mergeTagRule:g,mergeTagsConfig:x,hasMergeTagCallback:K,intl:I}),(0,s.hR)(e,{allowCustomFonts:M}),(0,s.vf)(e,{locale:j}),(0,s.al)(e,{textDirection:Z}),(0,s.eE)(e,{appearance:R}),T&&T(e);var r=N.current.querySelector(".editable")||N.current;C&&(e.inline=!0),w&&(e.fixed_toolbar_container=w),e.init_instance_callback=function(e){y&&e.focus(!1),h&&h(e),e.on("blur",(function(){if(G.current){k&&k(e);var r=(0,s.L5)(e);S&&S(r,e)}}))},e.setup=function(r){var n=["change"];b&&n.push("keyup"),r.on(n.join(" "),(function(){G.current=!0,k&&k(r);var e=(0,s.L5)(r);S&&S(e,r)})),r.on("TextColorChange",(function(r){var n="".concat((null==r?void 0:r.color)||"").trim().toLowerCase();n&&((e.color_map||[]).some((function(e,r){return r%2==0&&!(n!=="".concat(e||"").trim().toLowerCase())}))||setTimeout((function(){E(i.Nw.saveColor(n))}),100))}))},O.current&&O.current.remove();var t=i.h.getState(),o=i.wl.getCurrentSelection(t),a=i.wl.getDesignUI(t);(null!=o&&o.active||"classic"===a)&&(O.current=u.Z.EditorManager.createEditor(r,e),O.current.render())}),[K,l,g,F]),(0,o.useEffect)((function(){return function(){if(O.current){if(G.current){k&&k(O.current);var e=(0,s.L5)(O.current);S&&S(e,O.current)}O.current.destroy()}}}),[]),(0,o.useLayoutEffect)((function(){if(O.current&&G.current){k&&k(O.current);var e=(0,s.L5)(O.current);z.current!==e&&(z.current=e,S&&S(e,O.current))}}),[D,t]);var P=e.id,V=e.className,q=e.style;return o.createElement(p.Container,{id:P,className:V,style:q,ref:N},r)}));r.default=g;var v,d,p={Container:l.ZP.div(t||(v=["\n    a {\n      pointer-events: none !important;\n    }\n  "],d||(d=v.slice(0)),t=Object.freeze(Object.defineProperties(v,{raw:{value:Object.freeze(d)}}))))}}}]);